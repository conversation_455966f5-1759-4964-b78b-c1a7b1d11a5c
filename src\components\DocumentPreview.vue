<template>
  <Dialog
    :visible="visible"
    :header="document?.title || '文档预览'"
    :style="{ width: '90vw', height: '90vh' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    :resizable="true"
    class="document-preview-dialog"
    @update:visible="handleVisibleChange"
    @hide="handleClose"
  >
    <div class="preview-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p class="mt-3">正在加载文档...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: #e74c3c"></i>
        <p class="mt-3">{{ error }}</p>
        <Button label="重试" icon="pi pi-refresh" @click="loadDocument" class="mt-3" />
      </div>

      <!-- 文档预览内容 -->
      <div v-else-if="document" class="preview-content">
        <!-- 文档信息头部 -->
        <div class="document-info">
          <div class="info-item">
            <span class="label">文件名：</span>
            <span class="value">{{ document.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">类型：</span>
            <Tag :value="document.type" />
          </div>
          <div class="info-item">
            <span class="label">大小：</span>
            <span class="value">{{ formatFileSize(document.size) }}</span>
          </div>
          <div class="info-item">
            <span class="label">上传时间：</span>
            <span class="value">{{ formatDateLocale(document.uploadTime) }}</span>
          </div>
        </div>

        <!-- 预览内容区域 -->
        <div class="preview-area">
          <!-- 通用文档预览 - 直接使用iframe尝试预览 -->
          <div class="document-preview">
            <iframe
              v-if="documentUrl"
              :src="documentUrl"
              width="100%"
              height="600px"
              frameborder="0"
              style="border: none;"
              title="文档预览"
            ></iframe>
            <div v-else class="no-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #6c757d"></i>
              <p>文档预览</p>
              <p class="text-sm text-gray-500">正在加载文档内容...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          v-if="documentUrl"
          label="在新窗口中打开"
          icon="pi pi-external-link"
          @click="openInNewWindow"
          class="p-button-outlined mr-2"
        />
        <Button
          v-if="documentUrl"
          label="下载"
          icon="pi pi-download"
          @click="downloadDocument"
          class="p-button-outlined mr-2"
        />
        <Button label="关闭" icon="pi pi-times" @click="handleClose" />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import Tag from 'primevue/tag';
import { KnowledgeStrategyFactory } from '@/service/knowledge/KnowledgeStrategyFactory';
import { formatFileSize, formatDateLocale } from '@/utils/helpers';

// 组件属性
interface Props {
  visible: boolean;
  documentId?: string;
  datasetId?: string;
  document?: any; // 如果已有文档数据，可以直接传入
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  documentId: '',
  datasetId: '',
  document: null
});

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'close': [];
}>();

// 响应式数据
const loading = ref(false);
const error = ref('');
const document = ref<any>(null);
const documentUrl = ref('');

// Toast
const toast = useToast();

// 知识库服务
const knowledgeService = KnowledgeStrategyFactory.createStrategy();



// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    if (props.document) {
      // 如果直接传入了文档数据
      document.value = props.document;
      generateDocumentUrl();
    } else if (props.documentId && props.datasetId) {
      // 如果传入了ID，需要加载文档
      loadDocument();
    }
  } else {
    // 关闭时清理数据
    cleanupBlobUrl();
    document.value = null;
    documentUrl.value = '';
    error.value = '';
  }
});

// 监听documentId变化
watch(() => props.documentId, (newDocumentId) => {
  if (newDocumentId && props.datasetId && props.visible) {
    loadDocument();
  }
});

// 加载文档数据
async function loadDocument() {
  if (!props.documentId || !props.datasetId) {
    error.value = '缺少文档ID或数据集ID';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    // 设置数据集ID
    knowledgeService.setDatasetId(props.datasetId);

    // 获取文档详情
    const doc = await knowledgeService.getDocument(props.documentId);

    document.value = {
      id: doc.id,
      title: `文档 ${doc.id}`,
      type: doc.blob?.type || 'application/octet-stream',
      size: doc.blob?.size || 0,
      uploadTime: new Date(),
      content: '',
      metadata: {
        blobUrl: doc.blobUrl,
        originalData: doc.blob
      }
    };

    generateDocumentUrl();
  } catch (err) {
    console.error('加载文档失败:', err);
    error.value = err instanceof Error ? err.message : '加载文档失败';

    toast.add({
      severity: 'error',
      summary: '预览失败',
      detail: error.value,
      life: 3000
    });
  } finally {
    loading.value = false;
  }
}

// 生成文档URL
function generateDocumentUrl() {
  if (!document.value) return;

  // 如果文档元数据中有blobUrl，使用它（从API获取的数据）
  if (document.value.metadata?.blobUrl) {
    documentUrl.value = document.value.metadata.blobUrl;
    return;
  }

  // 对于有内容的文档，不需要URL
  if (document.value.content) {
    return;
  }
}

// 下载文档
function downloadDocument() {
  if (!document.value) {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档数据不可用',
      life: 3000
    });
    return;
  }

  // 优先使用原始数据进行下载
  if (document.value.metadata?.originalData) {
    const blob = new Blob([document.value.metadata.originalData], {
      type: document.value.metadata.contentType || 'application/octet-stream'
    });
    const url = URL.createObjectURL(blob);

    const link = window.document.createElement('a');
    link.href = url;
    link.download = document.value.title || 'document';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);

    // 清理URL
    URL.revokeObjectURL(url);
  } else if (documentUrl.value) {
    // 备用方案：使用URL下载
    const link = window.document.createElement('a');
    link.href = documentUrl.value;
    link.download = document.value.title || 'document';
    link.target = '_blank';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
  } else {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档链接不可用',
      life: 3000
    });
  }
}

// 在新窗口中打开文档
function openInNewWindow() {
  if (!documentUrl.value) {
    toast.add({
      severity: 'warn',
      summary: '无法打开',
      detail: '文档链接不可用',
      life: 3000
    });
    return;
  }

  window.open(documentUrl.value, '_blank');
}

// 处理visible变化
function handleVisibleChange(newVisible: boolean) {
  emit('update:visible', newVisible);
}

// 清理Blob URL
function cleanupBlobUrl() {
  if (document.value?.metadata?.blobUrl) {
    URL.revokeObjectURL(document.value.metadata.blobUrl);
  }
}

// 关闭对话框
function handleClose() {
  cleanupBlobUrl();
  emit('update:visible', false);
  emit('close');
}
</script>

<style scoped>
.document-preview-dialog {
  --dialog-border-radius: 12px;
}

.preview-container {
  height: calc(90vh - 120px);
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.document-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item .label {
  font-weight: 600;
  color: #6c757d;
}

.info-item .value {
  color: #495057;
}

.preview-area {
  flex: 1;
  overflow: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
}

.document-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.document-preview iframe {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text-content {
  width: 100%;
  height: 100%;
  padding: 1rem;
  overflow: auto;
}

.text-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
  padding: 2rem;
}

.no-preview p {
  margin: 0.5rem 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
