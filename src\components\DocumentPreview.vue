<template>
  <Dialog
    :visible="visible"
    :header="document?.title || '文档预览'"
    :style="{ width: '90vw', height: '90vh' }"
    :modal="true"
    :closable="true"
    :draggable="false"
    :resizable="true"
    class="document-preview-dialog"
    @update:visible="handleVisibleChange"
    @hide="handleClose"
  >
    <div class="preview-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p class="mt-3">正在加载文档...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: #e74c3c"></i>
        <p class="mt-3">{{ error }}</p>
        <Button label="重试" icon="pi pi-refresh" @click="loadDocument" class="mt-3" />
      </div>

      <!-- 文档预览内容 -->
      <div v-else-if="document" class="preview-content">
        <!-- 文档信息头部 -->
        <div class="document-info">
          <div class="info-item">
            <span class="label">文件名：</span>
            <span class="value">{{ document.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">类型：</span>
            <Tag :value="document.type" />
          </div>
          <div class="info-item">
            <span class="label">大小：</span>
            <span class="value">{{ formatFileSize(document.size) }}</span>
          </div>
          <div class="info-item">
            <span class="label">上传时间：</span>
            <span class="value">{{ formatDateLocale(document.uploadTime) }}</span>
          </div>
        </div>

        <!-- 预览内容区域 -->
        <div class="preview-area">
          <!-- PDF预览 -->
          <div v-if="isPDF" class="pdf-preview">
            <iframe
              v-if="documentUrl"
              :src="documentUrl"
              width="100%"
              height="600px"
              frameborder="0"
              style="border: none;"
              title="PDF预览"
            ></iframe>
            <div v-else class="no-preview">
              <i class="pi pi-file-pdf" style="font-size: 3rem; color: #e74c3c"></i>
              <p>PDF文档暂无法预览</p>
              <p class="text-sm text-gray-500">请点击下载按钮下载文档查看</p>
            </div>
          </div>

          <!-- 图片预览 -->
          <div v-else-if="isImage" class="image-preview">
            <img
              v-if="documentUrl"
              :src="documentUrl"
              :alt="document.title"
              style="max-width: 100%; max-height: 600px; object-fit: contain"
            />
            <div v-else class="no-preview">
              <i class="pi pi-image" style="font-size: 3rem; color: #3498db"></i>
              <p>图片暂无法预览</p>
              <p class="text-sm text-gray-500">请点击下载按钮下载图片查看</p>
            </div>
          </div>

          <!-- 文本预览 -->
          <div v-else-if="isText" class="text-preview">
            <div v-if="document.content" class="text-content">
              <pre>{{ document.content }}</pre>
            </div>
            <div v-else class="no-preview">
              <i class="pi pi-file-text" style="font-size: 3rem; color: #2ecc71"></i>
              <p>文本内容暂无法预览</p>
            </div>
          </div>

          <!-- Office文档预览 -->
          <div v-else-if="isOffice" class="office-preview">
            <div class="no-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #f39c12"></i>
              <p>Office文档预览</p>
              <p class="text-sm text-gray-500">支持的格式：.docx, .xlsx, .pptx</p>
              <Button
                v-if="documentUrl"
                label="在新窗口中打开"
                icon="pi pi-external-link"
                @click="openInNewWindow"
                class="mt-3"
              />
            </div>
          </div>

          <!-- 其他类型文档 -->
          <div v-else class="unsupported-preview">
            <div class="no-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #95a5a6"></i>
              <p>文档预览</p>
              <p class="text-sm text-gray-500">文件类型：{{ document.type }}</p>
              <Button
                v-if="documentUrl"
                label="在新窗口中打开"
                icon="pi pi-external-link"
                @click="openInNewWindow"
                class="mt-3"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          v-if="documentUrl"
          label="下载"
          icon="pi pi-download"
          @click="downloadDocument"
          class="p-button-outlined mr-2"
        />
        <Button label="关闭" icon="pi pi-times" @click="handleClose" />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { KnowledgeStrategyFactory } from '@/service/knowledge/KnowledgeStrategyFactory';
import { formatFileSize, formatDateLocale } from '@/utils/helpers';

// 组件属性
interface Props {
  visible: boolean;
  documentId?: string;
  datasetId?: string;
  document?: any; // 如果已有文档数据，可以直接传入
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  documentId: '',
  datasetId: '',
  document: null
});

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'close': [];
}>();

// 响应式数据
const loading = ref(false);
const error = ref('');
const document = ref<any>(null);
const documentUrl = ref('');

// Toast
const toast = useToast();

// 知识库服务
const knowledgeService = KnowledgeStrategyFactory.createStrategy();

// 计算属性 - 文档类型判断
const isPDF = computed(() => {
  return document.value?.type?.toLowerCase().includes('pdf') ||
         document.value?.title?.toLowerCase().endsWith('.pdf');
});

const isImage = computed(() => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const type = document.value?.type?.toLowerCase() || '';
  const title = document.value?.title?.toLowerCase() || '';

  return imageTypes.some(ext => type.includes(ext) || title.endsWith(`.${ext}`));
});

const isText = computed(() => {
  const textTypes = ['txt', 'md', 'text', 'plain'];
  const type = document.value?.type?.toLowerCase() || '';
  const title = document.value?.title?.toLowerCase() || '';

  return textTypes.some(ext => type.includes(ext) || title.endsWith(`.${ext}`));
});

const isOffice = computed(() => {
  const officeTypes = ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'];
  const type = document.value?.type?.toLowerCase() || '';
  const title = document.value?.title?.toLowerCase() || '';

  return officeTypes.some(ext => type.includes(ext) || title.endsWith(`.${ext}`));
});

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    if (props.document) {
      // 如果直接传入了文档数据
      document.value = props.document;
      generateDocumentUrl();
    } else if (props.documentId && props.datasetId) {
      // 如果传入了ID，需要加载文档
      loadDocument();
    }
  } else {
    // 关闭时清理数据
    document.value = null;
    documentUrl.value = '';
    error.value = '';
  }
});

// 监听documentId变化
watch(() => props.documentId, (newDocumentId) => {
  if (newDocumentId && props.datasetId && props.visible) {
    loadDocument();
  }
});

// 加载文档数据
async function loadDocument() {
  if (!props.documentId || !props.datasetId) {
    error.value = '缺少文档ID或数据集ID';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    // 设置数据集ID
    knowledgeService.setDatasetId(props.datasetId);

    // 获取文档详情
    const doc = await knowledgeService.getDocument(props.documentId);

    document.value = {
      id: doc.id,
      title: doc.name,
      type: doc.type || 'unknown',
      size: doc.size || 0,
      uploadTime: new Date(doc.created_at),
      content: doc.content,
      url: doc.metadata?.location || ''
    };

    generateDocumentUrl();
  } catch (err) {
    console.error('加载文档失败:', err);
    error.value = err instanceof Error ? err.message : '加载文档失败';

    toast.add({
      severity: 'error',
      summary: '预览失败',
      detail: error.value,
      life: 3000
    });
  } finally {
    loading.value = false;
  }
}

// 生成文档URL
function generateDocumentUrl() {
  if (!document.value) return;

  // 如果文档元数据中有fileUrl，使用它（直接文件访问）
  if (document.value.metadata?.fileUrl) {
    documentUrl.value = document.value.metadata.fileUrl;
    return;
  }

  // 如果文档有location URL，直接使用
  if (document.value.url) {
    documentUrl.value = document.value.url;
    return;
  }

  // 对于文本类型，如果有内容就不需要URL
  if (isText.value && document.value.content) {
    return;
  }

  // 其他情况，尝试构建API URL
  if (props.datasetId && props.documentId) {
    // 构建文档获取API URL
    documentUrl.value = `/api/v1/datasets/${props.datasetId}/documents/${props.documentId}`;
  }
}

// 下载文档
function downloadDocument() {
  if (!documentUrl.value) {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档链接不可用',
      life: 3000
    });
    return;
  }

  // 创建下载链接
  const link = window.document.createElement('a');
  link.href = documentUrl.value;
  link.download = document.value?.title || 'document';
  link.target = '_blank';
  window.document.body.appendChild(link);
  link.click();
  window.document.body.removeChild(link);
}

// 在新窗口中打开文档
function openInNewWindow() {
  if (!documentUrl.value) {
    toast.add({
      severity: 'warn',
      summary: '无法打开',
      detail: '文档链接不可用',
      life: 3000
    });
    return;
  }

  window.open(documentUrl.value, '_blank');
}

// 处理visible变化
function handleVisibleChange(newVisible: boolean) {
  emit('update:visible', newVisible);
}

// 关闭对话框
function handleClose() {
  emit('update:visible', false);
  emit('close');
}
</script>

<style scoped>
.document-preview-dialog {
  --dialog-border-radius: 12px;
}

.preview-container {
  height: calc(90vh - 120px);
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.document-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item .label {
  font-weight: 600;
  color: #6c757d;
}

.info-item .value {
  color: #495057;
}

.preview-area {
  flex: 1;
  overflow: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
}

.pdf-preview,
.image-preview,
.text-preview,
.office-preview,
.unsupported-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content {
  width: 100%;
  height: 100%;
  padding: 1rem;
  overflow: auto;
}

.text-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
  padding: 2rem;
}

.no-preview p {
  margin: 0.5rem 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
