<template>
  <div class="document-manager">
    <div class="header">
      <h1>文档管理</h1>
      <div class="actions">
        <FileUpload
          mode="basic"
          chooseLabel="上传文档"
          :disabled="!currentDataset || isUploading"
          :customUpload="true"
          @uploader="handleFileUpload"
          accept=".pdf,.docx,.doc,.txt,.md,.xlsx,.xls,.ppt,.pptx"
          :auto="true"
          chooseIcon="pi pi-upload"
          class="mr-2"
          :style="{ 'max-width': '180px' }"
        />
      </div>
    </div>

    <div class="filters place-content-between">
      <Dropdown
        v-model="currentDataset"
        :options="datasetOptions"
        optionLabel="name"
        optionValue="id"
        placeholder="选择知识库"
        class="ml-2"
        :disabled="isLoadingDatasets"
      />
      <InputGroup>
        <InputText
          v-model="filters.search"
          placeholder="搜索文档..."
          @keyup.enter="handleSearch"
        />
        <Button icon="pi pi-search" @click="handleSearch" />
      </InputGroup>
    </div>

    <DataTable
      :value="filteredDocuments"
      :loading="isLoading"
      responsiveLayout="scroll"
      :paginator="true"
      :rows="pagination.pageSize"
      :totalRecords="pagination.total"
      v-model:first="pagination.first"
      lazy
      @page="onPage"
      stripedRows
      rowHover
      class="p-datatable-sm"
      emptyMessage="没有找到文档"
      scrollHeight="calc(100vh - 350px)"
    >
      <Column field="title" header="标题"></Column>
      <Column field="type" header="类型" :style="{ width: '100px' }"></Column>
      <Column field="size" header="大小" :style="{ width: '100px' }">
        <template #body="slotProps">
          {{ formatFileSize(slotProps.data.size) }}
        </template>
      </Column>
      <Column field="uploadTime" header="上传时间" :style="{ width: '150px' }">
        <template #body="slotProps">
          {{ formatDateLocale(slotProps.data.uploadTime) }}
        </template>
      </Column>
      <Column field="run" header="状态" :style="{ width: '120px' }">
        <template #body="slotProps">
          <!-- <Tag :severity="getStatusSeverity(slotProps.data.run)">
            {{ getStatusLabel(slotProps.data.run) }}
          </Tag> -->
          <Tag v-if="slotProps.data.run === 'DONE'" severity="success">可用</Tag>
          <Tag v-else severity="secondary">不可用</Tag>
        </template>
      </Column>
      <Column header="操作" :exportable="false" style="min-width: 8rem">
        <template #body="slotProps">
          <div class="justify-content-center flex gap-2">
            <Button
              icon="pi pi-eye"
              class="p-button-rounded p-button-text"
              @click="showPreview(slotProps.data)"
            />
            <Button
              icon="pi pi-search"
              class="p-button-rounded p-button-text"
              @click="viewDocument(slotProps.data)"
            />
            <Button
              icon="pi pi-trash"
              class="p-button-rounded p-button-text p-button-danger"
              @click="confirmDelete(slotProps.data)"
            />
          </div>
        </template>
      </Column>

      <template #paginatorstart>
        <Button type="button" icon="pi pi-refresh" text @click="fetchDocuments" />
      </template>
      <template #paginatorend>
        <span class="pagination-info" v-if="pagination.total > 0">
          显示 {{ (pagination.page - 1) * pagination.pageSize + 1 }} -
          {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共
          {{ pagination.total }} 条记录
        </span>
      </template>
    </DataTable>

    <!-- 文档预览组件 -->
    <DocumentPreview
      v-model:visible="isPreviewVisible"
      :document-id="previewDocumentId"
      :dataset-id="currentDataset"
      @close="closePreview"
    />

    <ConfirmDialog></ConfirmDialog>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { useConfirm } from "primevue/useconfirm";
import { useToast } from "primevue/usetoast";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Dropdown from "primevue/dropdown";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import Tag from "primevue/tag";
import ConfirmDialog from "primevue/confirmdialog";
import Toast from "primevue/toast";
import FileUpload from "primevue/fileupload";
import InputGroup from "primevue/InputGroup";
import DocumentPreview from "@/components/DocumentPreview.vue";

import { KnowledgeStrategyFactory } from "../service/knowledge/KnowledgeStrategyFactory";
import { KnowledgeStrategy } from "../service/knowledge/KnowledgeStrategy";
import { Document as KnowledgeDocument } from "../types/Document";
import { formatFileSize, formatDateLocale, getFileExtension } from "../utils/helpers";

// 定义文档类型（原来从store导入）
interface Document {
  id: string;
  title: string;
  type: string;
  size: number;
  uploadTime: Date;
  tags?: string[];
  run: "UNSTART" | "RUNNING" | "CANCEL" | "DONE" | "FAIL";
  status: "0" | "1"; // 1：启用  ， 其他：未启用
  content?: string;
  url?: string;
}

// 添加状态相关函数
function getStatusSeverity(run: string) {
  switch (run) {
    case "UNSTART":
      return "info"; // 未解析
    case "RUNNING":
      return "warning"; // 解析中
    case "CANCEL":
      return "secondary"; // 取消
    case "DONE":
      return "success"; // 成功
    case "FAIL":
      return "danger"; // 失败
    default:
      return "info";
  }
}

const router = useRouter();
const confirm = useConfirm();
const toast = useToast();

// 数据加载状态
const isLoading = ref(false);
const isLoadingDatasets = ref(false);
const isUploading = ref(false);
const currentError = ref<string | null>(null);

// 文档列表和知识库列表状态
const documents = ref<Document[]>([]);
const datasetOptions = ref<Array<{ id: string; name: string }>>([]);
const currentDataset = ref<string>("");

// 获取知识库服务实例
const knowledgeService: KnowledgeStrategy = KnowledgeStrategyFactory.createStrategy();

// 添加分页参数
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  first: 0,
});

// 过滤器状态
const filters = ref({
  search: "",
  status: null,
  type: null,
});

// 定义状态选项
const statusOptions = [
  { label: "未解析", value: "UNSTART" },
  { label: "解析中", value: "RUNNING" },
  { label: "取消", value: "CANCEL" },
  { label: "成功", value: "DONE" },
  { label: "失败", value: "FAIL" },
];

// 基于状态选项获取状态标签
function getStatusLabel(run: string) {
  const option = statusOptions.find((opt) => opt.value === run);
  return option ? option.label : "未知";
}

const typeOptions = [
  { label: "全部", value: null },
  { label: "PDF", value: "pdf" },
  { label: "Word", value: "docx" },
  { label: "Excel", value: "xlsx" },
  { label: "Text", value: "txt" },
];

// 监听数据集变化
watch(currentDataset, (newDatasetId) => {
  if (newDatasetId) {
    knowledgeService.setDatasetId(newDatasetId);
    // 重置分页状态
    pagination.value.page = 1;
    pagination.value.first = 0;
    fetchDocuments();
  }
});

// 监听过滤器变化，如果是客户端过滤，注释掉这段即可
watch(
  filters,
  () => {
    // 当过滤条件变化时，重置分页并重新加载数据
    pagination.value.page = 1;
    pagination.value.first = 0;
    fetchDocuments();
  },
  { deep: true }
);

// 预览相关状态
const isPreviewVisible = ref(false);
const previewDocumentId = ref<string>('');

// 过滤文档 - 如果需要服务器端过滤，修改fetchDocuments方法，传入过滤参数
const filteredDocuments = computed(() => {
  // 如果使用服务器端过滤，这里直接返回documents
  return documents.value;

  // 如果使用客户端过滤，使用以下代码
  /*
  return documents.value.filter((doc) => {
    const matchesSearch = !filters.value.search || doc.title.toLowerCase().includes(filters.value.search.toLowerCase());
    const matchesStatus = !filters.value.status || doc.status === filters.value.status;
    const matchesType = !filters.value.type || doc.type === filters.value.type;

    return matchesSearch && matchesStatus && matchesType;
  });
  */
});

// 处理文件上传
async function handleFileUpload(event: any) {
  if (!currentDataset.value) {
    toast.add({
      severity: "warn",
      summary: "请先选择知识库",
      detail: "上传文档前需要选择目标知识库",
      life: 3000,
    });
    return;
  }

  const file = event.files[0];
  if (!file) {
    return;
  }

  isUploading.value = true;

  try {
    // 设置元数据（可根据需要扩展）
    const metadata = {
      tags: [getFileExtension(file.name)], // 使用文件扩展名作为标签
      source: "manual_upload",
      upload_time: new Date().toISOString(),
    };

    // 调用知识库服务上传文件
    const result = await knowledgeService.uploadDocument(file, metadata);

    toast.add({
      severity: "success",
      summary: "上传成功",
      detail: `文档"${file.name}"已上传到知识库`,
      life: 3000,
    });

    // 刷新文档列表
    await fetchDocuments();
  } catch (error) {
    console.error("上传文档失败:", error);
    currentError.value = error instanceof Error ? error.message : String(error);

    toast.add({
      severity: "error",
      summary: "上传失败",
      detail: currentError.value,
      life: 5000,
    });
  } finally {
    isUploading.value = false;
  }
}

// 查看文档详情
function viewDocument(document: Document) {
  router.push(`/documents/${document.id}`);
}

// 预览文档
function showPreview(document: Document) {
  previewDocumentId.value = document.id;
  isPreviewVisible.value = true;
}

// 关闭预览
function closePreview() {
  isPreviewVisible.value = false;
  previewDocumentId.value = '';
}

// 确认删除
function confirmDelete(document: Document) {
  confirm.require({
    message: `确定要删除文档 "${document.title}" 吗？`,
    header: "确认删除",
    icon: "pi pi-exclamation-triangle",
    acceptClass: "p-button-danger",
    accept: () => {
      deleteDocument(document);
    },
  });
}

// 删除文档
async function deleteDocument(document: Document) {
  isLoading.value = true;

  try {
    // 直接调用知识库服务删除文档
    await knowledgeService.deleteDocument(document.id);

    // 从本地列表中移除
    const index = documents.value.findIndex((doc) => doc.id === document.id);
    if (index !== -1) {
      documents.value.splice(index, 1);
    }

    toast.add({
      severity: "success",
      summary: "删除成功",
      detail: `文档 "${document.title}" 已删除`,
      life: 3000,
    });
  } catch (error) {
    currentError.value = error instanceof Error ? error.message : String(error);
    toast.add({
      severity: "error",
      summary: "删除失败",
      detail: currentError.value,
      life: 3000,
    });
  } finally {
    isLoading.value = false;
  }
}

// 下载文档
function downloadDocument(document: Document) {
  if (document.url) {
    // 实际环境中应该使用更可靠的下载方法
    window.open(document.url, "_blank");
  } else {
    toast.add({
      severity: "info",
      summary: "无法下载",
      detail: "文档链接不可用",
      life: 3000,
    });
  }
}

// 获取数据集列表
async function fetchDatasets() {
  if (isLoadingDatasets.value) {
    console.log("已有数据集加载请求正在进行中，请稍后再试...");
    return;
  }

  isLoadingDatasets.value = true;
  currentError.value = null;

  try {
    // 直接从知识库服务获取数据集列表
    const datasetList = await knowledgeService.listDatasets();

    // 转换为下拉框所需格式
    datasetOptions.value = datasetList.map((dataset: any) => ({
      id: dataset.id,
      name: dataset.name || `知识库 ${dataset.id}`,
    }));

    // 如果有数据集且未选择，则默认选中第一个
    if (datasetOptions.value.length > 0 && !currentDataset.value) {
      const newDatasetId = datasetOptions.value[0].id;

      // 更新知识库服务的数据集ID
      knowledgeService.setDatasetId(newDatasetId);
      currentDataset.value = newDatasetId;
    }
  } catch (error) {
    console.error("获取数据集列表失败:", error);
    currentError.value = error instanceof Error ? error.message : String(error);

    toast.add({
      severity: "error",
      summary: "获取数据集失败",
      detail: currentError.value,
      life: 3000,
    });
  } finally {
    isLoadingDatasets.value = false;
  }
}

// 获取文档列表
async function fetchDocuments() {
  isLoading.value = true;
  currentError.value = null;

  console.log(
    `获取数据集[${currentDataset.value}]的文档，页码:${pagination.value.page}, 每页数量:${
      pagination.value.pageSize
    }, 筛选条件:${filters.value.search ? filters.value.search : "无"}`
  );

  try {
    if (!knowledgeService) {
      console.error("知识库服务未初始化");
      return;
    }

    // 注意：RAGFlow的API设计中：
    // 1. listDocuments: 获取文档列表，支持通过params中的keywords参数进行标题筛选
    // 2. searchDocuments: 检索文档分块内容，用于知识问答，不用于文档管理

    let result;

    // 使用listDocuments获取文档列表，并通过params传递关键词
    const params: Record<string, any> = {
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
    };

    // 如果有搜索关键词，设置到params
    if (filters.value.search) {
      // RAGFlow API支持通过keywords参数筛选文档标题
      params.keywords = filters.value.search;
    }

    // 如果是RAGFlowKnowledgeStrategy的实例，我们可以直接修改其调用方式
    // 但为了接口通用性，我们需要按照KnowledgeStrategy接口定义调用
    result = await knowledgeService.listDocuments(
      pagination.value.page,
      pagination.value.pageSize,
      {
        // 如果有搜索关键词，传递到接口
        keywords: filters.value.search || "",
      }
    );

    // 注意: 上面的调用不支持传递keywords参数
    // 如果需要支持标题筛选，需要修改KnowledgeStrategy接口和实现类

    // 更新分页信息
    pagination.value.total = result.total;

    // 将知识库文档转换为app文档格式
    documents.value = result.documents.map((doc: KnowledgeDocument) => ({
      id: doc.id,
      title: doc.name,
      type: doc.type || "unknown",
      size: doc.size || 0,
      uploadTime: new Date(doc.created_at),
      run: doc.metadata?.run,
      status: doc.metadata?.status,
      content: doc.content,
      url: doc.metadata?.location || "",
    }));
  } catch (error) {
    currentError.value = error instanceof Error ? error.message : String(error);
    console.error("获取文档列表失败:", error);

    toast.add({
      severity: "error",
      summary: "获取文档失败",
      detail: currentError.value,
      life: 3000,
    });
  } finally {
    isLoading.value = false;
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化知识库服务
  try {
    // 获取数据集列表
    fetchDatasets();
  } catch (error) {
    console.error("初始化失败:", error);
    currentError.value = error instanceof Error ? error.message : String(error);
  }
});

function onPage(event: any) {
  pagination.value.page = event.page + 1;
  pagination.value.first = event.first;
  fetchDocuments();
}

function handleSearch() {
  // 实现搜索逻辑
  console.log("搜索:", filters.value.search);
  fetchDocuments();
}

// 映射知识库文档状态到应用状态
function mapDocumentStatus(run?: string): Document["run"] {
  if (!run) return "UNSTART"; // 未解析

  // 直接映射知识库状态到statusOptions中的值
  switch (run) {
    case "running":
      return "RUNNING"; // 解析中
    case "cancelled":
      return "CANCEL"; // 取消
    case "completed":
      return "DONE"; // 成功
    case "failed":
      return "FAIL"; // 失败
    default:
      return "UNSTART"; // 默认未解析
  }
}
</script>

<style scoped>
.document-manager {
  padding: 1.5rem;
  max-width: 1500px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 1rem;
}

.header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

.actions {
  display: flex;
  gap: 0.75rem;
}

.filters {
  display: flex;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

.p-inputgroup {
  width: 300px;
  max-width: 100%;
}



.preview-not-available {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 350px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #dee2e6;
}

.pagination-info {
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .p-inputgroup {
    width: 100%;
  }

  .filters .ml-2 {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .actions {
    margin-top: 1rem;
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
