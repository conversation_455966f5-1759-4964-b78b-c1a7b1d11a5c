<template>
  <div class="document-search">
    <div class="header">
      <h1>文档检索</h1>
    </div>

    <div class="search-container">
      <div class="search-box">
        <span class="p-input-icon-left p-input-icon-right w-full">
          <i class="pi pi-search" />
          <InputText
            v-model="searchQuery"
            placeholder="输入关键词进行检索..."
            class="w-full"
            @keyup.enter="performSearch"
          />
          <i v-if="searchQuery" class="pi pi-times" @click="clearSearch" />
        </span>
      </div>
      <div class="search-options">
        <div class="dataset-selection">
          <span>知识库：</span>
          <Dropdown
            v-model="currentDataset"
            :options="datasetOptions"
            optionLabel="name"
            optionValue="id"
            placeholder="选择知识库"
            class="ml-2"
            :disabled="isLoadingDatasets"
          />
          <Button
            icon="pi pi-refresh"
            class="p-button-text p-button-rounded ml-2"
            @click="fetchDatasets"
            :disabled="isLoadingDatasets"
          />
        </div>
        <div class="p-field-checkbox ml-4">
          <Checkbox v-model="searchOptions.useAI" inputId="useAI" binary />
          <label for="useAI" class="ml-2">启用AI辅助检索</label>
        </div>
        <div class="p-field-checkbox ml-4">
          <Checkbox v-model="searchOptions.exactMatch" inputId="exactMatch" binary />
          <label for="exactMatch" class="ml-2">精确匹配</label>
        </div>
      </div>
      <div class="search-actions">
        <Button label="搜索" icon="pi pi-search" @click="performSearch" />
      </div>
    </div>

    <div v-if="isSearching" class="search-progress">
      <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
      <span class="ml-3">正在检索中...</span>
    </div>

    <TabView v-if="hasSearched && !isSearching" class="search-results">
      <TabPanel
        header="文档结果"
        value="documents"
        :pt="{ root: { value: 'documents' } }"
      >
        <div v-if="searchResults.documents.length === 0" class="no-results">
          <i class="pi pi-info-circle" style="font-size: 2rem"></i>
          <p>未找到匹配的文档</p>
        </div>
        <div v-else class="result-list">
          <div
            v-for="(doc, index) in searchResults.documents"
            :key="index"
            class="result-item"
          >
            <div class="result-header">
              <h3>{{ doc.name }}</h3>
              <Tag :value="doc.type" />
            </div>
            <div class="result-snippet" v-html="doc.content"></div>
            <div class="result-meta">
              <span><i class="pi pi-calendar"></i> {{ formatDate(doc.created_at || '') }}</span>
              <span v-if="doc.metadata && doc.metadata.tags"
                ><i class="pi pi-tag"></i>
                {{
                  Array.isArray(doc.metadata.tags)
                    ? doc.metadata.tags.join(", ")
                    : doc.metadata.tags
                }}</span
              >
              <span v-if="doc.metadata && doc.metadata.similarity"
                ><i class="pi pi-chart-line"></i>
                {{ formatSimilarity(doc.metadata.similarity) }}</span
              >
            </div>
            <div class="result-actions">
              <Button
                label="预览"
                icon="pi pi-eye"
                class="p-button-sm"
                @click="showPreview(doc)"
              />
              <Button
                label="查看"
                icon="pi pi-search"
                class="p-button-sm p-button-outlined ml-2"
                @click="viewDocument(doc)"
              />
              <Button
                label="下载"
                icon="pi pi-download"
                class="p-button-sm p-button-outlined ml-2"
                @click="downloadDocument(doc)"
              />
            </div>
          </div>
        </div>

        <Paginator
          v-if="searchResults.documents.length > 0"
          :rows="pageSize"
          :totalRecords="searchResults.total"
          :first="(currentPage - 1) * pageSize"
          @page="onPageChange($event)"
          template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
          class="mt-4"
        >
          <template #end>
            <span class="pagination-info" v-if="searchResults.total > 0">
              显示 {{ (currentPage - 1) * pageSize + 1 }} -
              {{ Math.min(currentPage * pageSize, searchResults.total) }} 条，共
              {{ searchResults.total }} 条记录
            </span>
          </template>
        </Paginator>
      </TabPanel>

      <TabPanel
        header="AI摘要"
        value="ai-summary"
        :pt="{ root: { value: 'ai-summary' } }"
        v-if="searchOptions.useAI"
      >
        <div v-if="!searchResults.aiSummary" class="no-results">
          <i class="pi pi-info-circle" style="font-size: 2rem"></i>
          <p>AI尚未生成摘要</p>
        </div>
        <div v-else class="ai-summary">
          <div class="ai-header">
            <h3><i class="pi pi-star"></i> AI生成的回答</h3>
          </div>
          <div class="ai-content">
            <p v-html="searchResults.aiSummary"></p>
          </div>
          <div class="ai-sources">
            <h4>参考来源：</h4>
            <ul>
              <li v-for="(source, index) in searchResults.aiSources" :key="index">
                <a href="#" @click.prevent="viewDocument(source)">{{ source.name }}</a>
              </li>
            </ul>
          </div>
        </div>
      </TabPanel>
    </TabView>

    <!-- 文档预览组件 -->
    <DocumentPreview
      v-model:visible="isPreviewVisible"
      :document-id="previewDocumentId"
      :dataset-id="currentDataset"
      @close="closePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "@/stores/app";
import { useToast } from "primevue/usetoast";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Checkbox from "primevue/checkbox";
import Dropdown from "primevue/dropdown";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import ProgressSpinner from "primevue/progressspinner";
import Tag from "primevue/tag";
import Paginator from "primevue/paginator";
import DocumentPreview from "@/components/DocumentPreview.vue";
import { KnowledgeStrategyFactory } from "@/service/knowledge/KnowledgeStrategyFactory";
import { Document } from "@/types/Document";

const router = useRouter();
const appStore = useAppStore();
const toast = useToast();

const searchQuery = ref("");
const searchOptions = ref({
  useAI: true,
  exactMatch: false,
});

const isSearching = ref(false);
const isLoadingDatasets = ref(false);
const hasSearched = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);

// 预览相关状态
const isPreviewVisible = ref(false);
const previewDocumentId = ref<string>('');

// 添加数据集相关状态
const datasetOptions = ref<Array<{ id: string; name: string }>>([]);
const currentDataset = ref<string>("");

// 创建知识库服务实例
const knowledgeService = KnowledgeStrategyFactory.createStrategy();

const searchResults = ref<{
  documents: Document[];
  total: number;
  page: number;
  pageSize: number;
  aiSummary: string;
  aiSources: Document[];
}>({
  documents: [],
  total: 0,
  page: 1,
  pageSize: 10,
  aiSummary: "",
  aiSources: [],
});

function clearSearch() {
  searchQuery.value = "";
  hasSearched.value = false;
  searchResults.value.documents = [];
  searchResults.value.aiSummary = "";
  searchResults.value.aiSources = [];
}

// 获取数据集列表
async function fetchDatasets() {
  if (isLoadingDatasets.value) {
    toast.add({
      severity: "info",
      summary: "操作提示",
      detail: "数据集加载请求正在进行中，请稍后再试",
      life: 3000,
    });
    return;
  }

  isLoadingDatasets.value = true;

  try {
    // 从知识库服务获取数据集列表
    const datasetList = await knowledgeService.listDatasets();

    // 转换为下拉框所需格式
    datasetOptions.value = datasetList.map((dataset: any) => ({
      id: dataset.id,
      name: dataset.name || `知识库 ${dataset.id}`,
    }));

    // 如果有数据集且未选择，则默认选中第一个
    if (datasetOptions.value.length > 0 && !currentDataset.value) {
      const newDatasetId = datasetOptions.value[0].id;

      // 更新知识库服务的数据集ID
      knowledgeService.setDatasetId(newDatasetId);
      currentDataset.value = newDatasetId;
    }

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "已获取知识库列表",
      life: 3000,
    });
  } catch (error) {
    console.error("获取数据集列表失败:", error);

    toast.add({
      severity: "error",
      summary: "获取数据集失败",
      detail: error instanceof Error ? error.message : "未知错误",
      life: 3000,
    });
  } finally {
    isLoadingDatasets.value = false;
  }
}

// 监听数据集变化
watch(currentDataset, (newDatasetId) => {
  if (newDatasetId) {
    // 更新知识库服务的数据集ID
    knowledgeService.setDatasetId(newDatasetId);

    // 如果已有搜索，则自动重新搜索
    if (hasSearched.value && searchQuery.value) {
      performSearch();
    }
  }
});

async function performSearch() {
  if (!searchQuery.value.trim()) return;

  isSearching.value = true;
  hasSearched.value = true;

  try {
    if (!currentDataset.value) {
      throw new Error("请先选择知识库");
    }

    // 调用搜索接口
    const result = await knowledgeService.searchDocuments(
      searchQuery.value,
      currentPage.value,
      pageSize.value
    );

    // 更新搜索结果
    searchResults.value.documents = result.documents;
    searchResults.value.total = result.total;
    searchResults.value.page = result.page;
    searchResults.value.pageSize = result.pageSize;

    // 如果启用AI摘要，这里应该有一个调用AI生成摘要的代码
    // 示例代码，实际上这部分需要根据后端接口来实现
    if (searchOptions.value.useAI && result.documents.length > 0) {
      // 模拟AI摘要生成
      searchResults.value.aiSummary = `根据搜索结果，<span class="highlight">${searchQuery.value}</span> 在多个文档中都有提及。综合来看，这些文档主要讨论了该关键词的重要性和应用场景。`;
      searchResults.value.aiSources = result.documents.slice(0, 3); // 取前三个文档作为来源
    } else {
      searchResults.value.aiSummary = "";
      searchResults.value.aiSources = [];
    }
  } catch (error) {
    console.error("搜索失败:", error);
    toast.add({
      severity: "error",
      summary: "搜索失败",
      detail: error instanceof Error ? error.message : "未知错误",
      life: 3000,
    });

    // 清空结果
    searchResults.value.documents = [];
    searchResults.value.total = 0;
  } finally {
    isSearching.value = false;
  }
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

function formatSimilarity(similarity: number): string {
  return (similarity * 100).toFixed(2) + "%";
}

function viewDocument(doc: Document) {
  router.push(`/documents/${doc.id}`);
}

function downloadDocument(doc: Document) {
  if (doc.metadata?.location) {
    window.open(doc.metadata.location, "_blank");
  } else {
    toast.add({
      severity: "info",
      summary: "无法下载",
      detail: "文档链接不可用",
      life: 3000,
    });
  }
}

// 预览文档
function showPreview(doc: Document) {
  // 对于搜索结果，文档ID可能在metadata中
  const documentId = doc.metadata?.document_id || doc.id;
  previewDocumentId.value = documentId;
  isPreviewVisible.value = true;
}

// 关闭预览
function closePreview() {
  isPreviewVisible.value = false;
  previewDocumentId.value = '';
}

function onPageChange(event: any) {
  currentPage.value = Math.floor(event.first / event.rows) + 1;
  pageSize.value = event.rows;
  performSearch();
}

// 初始加载时检查知识库配置
onMounted(() => {
  // 获取数据集列表
  fetchDatasets();
});
</script>

<style scoped>
.document-search {
  padding: 1.5rem;
  max-width: 1500px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 1rem;
}

.header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
}

.search-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.search-box {
  margin-bottom: 1.5rem;
  position: relative;
}

.search-box .p-input-icon-left,
.search-box .p-input-icon-right {
  position: relative;
  display: inline-block;
  width: 100%;
}

.search-box .p-input-icon-left i:first-child {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-box .p-input-icon-right i:last-child {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  cursor: pointer;
}

.search-box .p-inputtext {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.search-options {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.search-actions {
  display: flex;
  justify-content: flex-end;
}

.search-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.search-results {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6c757d;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
}

.result-item {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1.5rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.result-header h3 {
  margin: 0;
  color: #1976d2;
  font-size: 1.2rem;
}

.result-snippet {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #2c3e50;
}

.result-meta {
  display: flex;
  gap: 1rem;
  color: #6c757d;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.result-actions {
  display: flex;
  gap: 0.5rem;
}

.ai-summary {
  padding: 1.5rem;
}

.ai-header {
  margin-bottom: 1.5rem;
  color: #4caf50;
}

.ai-content {
  background-color: #f8f9fa;
  border-left: 4px solid #4caf50;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
  line-height: 1.6;
  color: #2c3e50;
}

.ai-sources {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 4px;
}

.ai-sources h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #2c3e50;
}

.ai-sources ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #2c3e50;
}

.highlight {
  background-color: #fff59d;
  padding: 0 3px;
  border-radius: 2px;
}

.dataset-selection {
  display: flex;
  align-items: center;
}

.pagination-info {
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-options {
    flex-direction: column;
    align-items: stretch;
  }

  .search-options .ml-2,
  .search-options .ml-4 {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .dataset-selection {
    width: 100%;
  }
}
</style>
